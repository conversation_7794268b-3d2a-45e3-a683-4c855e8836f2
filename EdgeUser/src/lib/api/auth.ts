// 认证相关API服务

// EdgeAdmin登录请求类型
export interface AdminLoginRequest {
  token: string
  username: string
  password: string
  otpCode?: string
  remember?: boolean
}

// EdgeAdmin登录响应类型
export interface AdminLoginResponse {
  isOk: boolean
  adminId?: number
  requireOTP?: boolean
  message?: string
  remember?: boolean
  sid?: string
  localSid?: string
  ip?: string
}

// 获取登录token响应类型
export interface LoginTokenResponse {
  token: string
  isDemo: boolean
  systemName: string
  showVersion: boolean
  version: string
  faviconFileId: string
  rememberLogin: boolean
}

// 用户信息类型（匹配AuthUser类型）
export interface UserInfo {
  accountNo: string
  email: string  // 确保email是string类型，不是optional
  username: string
  userId: number
  role: string[]
  exp: number
}

// EdgeOpenAPI CSRF token响应类型
export interface CSRFTokenResponse {
  token: string
}

// EdgeOpenAPI用户登录请求类型
export interface EdgeOpenAPILoginRequest {
  username: string
  password: string
  csrfToken: string
  remember?: boolean
}

// EdgeOpenAPI用户登录响应类型
export interface EdgeOpenAPILoginResponse {
  success: boolean
  message?: string
  user_id?: number
  access_token?: string
  expires_at?: number
  token_type?: string
}

// API配置
const ADMIN_ENDPOINT = import.meta.env.VITE_ADMIN_ENDPOINT ||
  (import.meta.env.DEV ? '' : 'http://127.0.0.1:7788')

// EdgeOpenAPI配置
const EDGE_OPENAPI_ENDPOINT = import.meta.env.VITE_EDGE_OPENAPI_ENDPOINT ||
  (import.meta.env.DEV ? 'http://localhost:8080' : 'http://127.0.0.1:8080')

// 创建基础API客户端
class ApiClient {
  private baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return response.json()
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(data),
      credentials: 'include'
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`)
    }

    return response.json()
  }
}

// 认证服务
class AuthService {
  private client: ApiClient

  constructor() {
    this.client = new ApiClient(ADMIN_ENDPOINT)
  }

  // 获取登录token
  async getLoginToken(): Promise<LoginTokenResponse> {
    try {
      return await this.client.get<LoginTokenResponse>('/openapi/auth/login')
    } catch (error) {
      console.error('获取登录token失败:', error)
      throw error
    }
  }

  // 管理员登录
  async loginAdmin(request: AdminLoginRequest): Promise<AdminLoginResponse> {
    try {
      return await this.client.post<AdminLoginResponse>('/openapi/auth/login', request)
    } catch (error) {
      console.error('管理员登录失败:', error)
      throw error
    }
  }

  // 用户登录（兼容原有接口）
  async loginUser(username: string, password: string, remember: boolean = false): Promise<AdminLoginResponse> {
    try {
      // 先获取token
      const tokenResponse = await this.getLoginToken()

      // 然后进行登录
      const loginRequest: AdminLoginRequest = {
        token: tokenResponse.token,
        username,
        password,
        remember
      }

      return await this.loginAdmin(loginRequest)
    } catch (error) {
      console.error('用户登录失败:', error)
      throw error
    }
  }

  // EdgeOpenAPI: 获取CSRF token
  async getCSRFToken(): Promise<CSRFTokenResponse> {
    try {
      const response = await fetch(`${EDGE_OPENAPI_ENDPOINT}/csrf/token`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return { token: result.data.token }
    } catch (error) {
      console.error('获取CSRF token失败:', error)
      throw error
    }
  }

  // EdgeOpenAPI: 用户登录
  async loginUserWithEdgeOpenAPI(username: string, password: string, remember: boolean = false): Promise<EdgeOpenAPILoginResponse> {
    try {
      // 先获取CSRF token
      const csrfResponse = await this.getCSRFToken()

      // 然后进行登录
      const loginRequest: EdgeOpenAPILoginRequest = {
        username,
        password,
        csrfToken: csrfResponse.token,
        remember
      }

      const response = await fetch(`${EDGE_OPENAPI_ENDPOINT}/api/v1/auth/login-csrf`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(loginRequest)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return result.data
    } catch (error) {
      console.error('EdgeOpenAPI用户登录失败:', error)
      throw error
    }
  }

  // 创建用户信息（从登录响应）
  createUserInfo(response: AdminLoginResponse, username: string): UserInfo {
    return {
      accountNo: response.adminId?.toString() || '',
      email: this.getUsernameType(username) === 'email' ? username : '',
      username,
      userId: response.adminId || 0,
      role: ['user'],
      exp: Date.now() + 24 * 60 * 60 * 1000 // 24小时后过期
    }
  }

  // 从EdgeOpenAPI响应创建用户信息
  createUserInfoFromEdgeOpenAPI(response: EdgeOpenAPILoginResponse, username: string): UserInfo {
    return {
      accountNo: response.user_id?.toString() || '',
      email: this.getUsernameType(username) === 'email' ? username : '',
      username,
      userId: response.user_id || 0,
      role: ['user'],
      exp: response.expires_at || (Date.now() + 24 * 60 * 60 * 1000) // 使用响应中的过期时间或默认24小时
    }
  }

  // 生成访问令牌
  generateAccessToken(adminId: number, localSid?: string): string {
    const payload = {
      adminId,
      localSid,
      exp: Date.now() + 24 * 60 * 60 * 1000
    }
    return btoa(JSON.stringify(payload))
  }

  // 获取用户名类型
  getUsernameType(username: string): 'email' | 'phone' | 'username' {
    if (username.includes('@')) {
      return 'email'
    }
    if (/^\d+$/.test(username)) {
      return 'phone'
    }
    return 'username'
  }

  // 验证用户名
  validateUsername(username: string): boolean {
    if (!username || username.length < 3) {
      return false
    }
    
    const type = this.getUsernameType(username)
    switch (type) {
      case 'email':
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(username)
      case 'phone':
        return /^\d{10,15}$/.test(username)
      case 'username':
        return /^[a-zA-Z0-9_-]{3,20}$/.test(username)
      default:
        return false
    }
  }
}

// 导出实例
export const authService = new AuthService()
export default authService 
