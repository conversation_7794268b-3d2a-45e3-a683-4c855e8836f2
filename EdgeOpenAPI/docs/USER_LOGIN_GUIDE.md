# EdgeOpenAPI User登录功能使用指南

## 概述

基于EdgeAdmin的管理端用户登录流程，EdgeOpenAPI现已实现完整的User登录功能，包括CSRF token保护和与EdgeUser前端的集成。

## 功能特性

### 1. CSRF Token保护
- **端点**: `GET /csrf/token`
- **功能**: 获取CSRF token用于登录请求
- **安全**: 30分钟有效期，一次性使用
- **速率限制**: 防止频繁请求

### 2. User登录
- **端点**: `POST /api/v1/auth/login-csrf`
- **功能**: 用户登录验证
- **支持**: 用户名、邮箱、手机号登录
- **验证**: CSRF token + 用户凭据双重验证

### 3. 配置管理
- **配置文件**: `configs/config.yaml`
- **API节点**: 支持配置化的EdgeAPI连接
- **安全密钥**: 可配置的CSRF token生成密钥

## API使用流程

### 步骤1: 获取CSRF Token

```bash
curl -X GET http://localhost:8080/csrf/token
```

响应示例:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "token": "MTcwNjg2NDQwMGY5YjM4ZjM5ZjE2YjY4ZjE2YjY4ZjE2YjY4ZjE2YjY4ZjE2YjY4"
  }
}
```

### 步骤2: 用户登录

```bash
curl -X POST http://localhost:8080/api/v1/auth/login-csrf \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password",
    "csrfToken": "your_csrf_token",
    "remember": false
  }'
```

成功响应示例:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "success": true,
    "message": "登录成功",
    "user_id": 123,
    "token_type": "Bearer"
  }
}
```

## EdgeUser集成

### 环境变量配置

在EdgeUser项目的`.env`文件中配置:

```bash
# EdgeOpenAPI 服务端点
VITE_EDGE_OPENAPI_ENDPOINT=http://127.0.0.1:8080

# 是否使用EdgeOpenAPI进行用户登录
VITE_USE_EDGE_OPENAPI=true
```

### 前端使用

EdgeUser会自动根据`VITE_USE_EDGE_OPENAPI`环境变量选择登录方式:

- `true`: 使用EdgeOpenAPI的CSRF保护登录
- `false`: 使用原有的EdgeAdmin登录

## 配置说明

### EdgeOpenAPI配置 (`configs/config.yaml`)

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8080
  mode: "release"

# EdgeAPI连接配置
edgeapi:
  endpoint: "localhost:8001"
  timeout: "30s"

# API节点认证
api_node:
  node_id: "your_api_node_id"
  secret: "your_api_node_secret"

# 安全配置
security:
  secret: "EdgeOpenAPI_CSRF_Secret_Key_2024"
```

## 启动服务

### 1. 启动EdgeAPI服务
确保EdgeAPI服务正在运行在配置的端点上。

### 2. 启动EdgeOpenAPI
```bash
cd EdgeOpenAPI
go run cmd/server/main.go
```

或使用配置文件:
```bash
go run cmd/server/main.go -config configs/config.yaml
```

### 3. 启动EdgeUser
```bash
cd EdgeUser
npm run dev
```

## 测试验证

使用提供的测试脚本:
```bash
cd EdgeOpenAPI
./test_login.sh
```

## 安全注意事项

1. **CSRF Token**: 每个token只能使用一次，30分钟有效期
2. **API节点认证**: 确保API节点ID和密钥正确配置
3. **HTTPS**: 生产环境建议使用HTTPS
4. **速率限制**: 内置基础速率限制，可根据需要扩展

## 故障排除

### 常见问题

1. **CSRF token无效**
   - 检查token是否已过期
   - 确认token未被重复使用

2. **连接EdgeAPI失败**
   - 检查EdgeAPI服务状态
   - 验证API节点配置

3. **登录失败**
   - 确认用户凭据正确
   - 检查EdgeAPI用户权限设置

### 日志查看

EdgeOpenAPI使用结构化日志，可通过配置调整日志级别:
```yaml
logging:
  level: "debug"  # debug, info, warn, error
```

## 下一步

- [ ] 实现OTP二次验证支持
- [ ] 添加更多安全中间件
- [ ] 扩展用户管理功能
- [ ] 完善错误处理和日志记录
